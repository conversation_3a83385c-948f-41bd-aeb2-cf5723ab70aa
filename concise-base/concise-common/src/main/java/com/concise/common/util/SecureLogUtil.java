package com.concise.common.util;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;

/**
 * 安全日志工具类
 * 用于避免在日志中暴露敏感信息，防止信息泄露漏洞
 * 
 * <AUTHOR>
 * @date 2024-08-04
 */
@Slf4j
public class SecureLogUtil {

    /**
     * 记录安全的错误日志
     * 在ERROR级别只记录异常类型和简要描述，详细堆栈信息记录在DEBUG级别
     * 
     * @param logger 日志记录器
     * @param message 错误消息
     * @param exception 异常对象
     */
    public static void logSecureError(Logger logger, String message, Exception exception) {
        if (logger == null || exception == null) {
            return;
        }
        
        // ERROR级别只记录异常类型，避免暴露敏感信息
        logger.error("{}: {}", message, exception.getClass().getSimpleName());
        
        // DEBUG级别记录详细异常信息，用于开发调试
        logger.debug(message + "详细异常信息", exception);
    }

    /**
     * 记录安全的错误日志（带参数）
     * 
     * @param logger 日志记录器
     * @param message 错误消息模板
     * @param exception 异常对象
     * @param args 消息参数
     */
    public static void logSecureError(Logger logger, String message, Exception exception, Object... args) {
        if (logger == null || exception == null) {
            return;
        }
        
        // 构建完整的错误消息
        String fullMessage = String.format(message.replace("{}", "%s"), args);
        
        // ERROR级别只记录异常类型
        logger.error("{}: {}", fullMessage, exception.getClass().getSimpleName());
        
        // DEBUG级别记录详细异常信息
        logger.debug(fullMessage + "详细异常信息", exception);
    }

    /**
     * 记录安全的警告日志
     * 
     * @param logger 日志记录器
     * @param message 警告消息
     * @param exception 异常对象（可选）
     */
    public static void logSecureWarn(Logger logger, String message, Exception exception) {
        if (logger == null) {
            return;
        }
        
        if (exception != null) {
            // WARN级别只记录异常类型
            logger.warn("{}: {}", message, exception.getClass().getSimpleName());
            
            // DEBUG级别记录详细异常信息
            logger.debug(message + "详细异常信息", exception);
        } else {
            logger.warn(message);
        }
    }

    /**
     * 脱敏处理敏感信息
     * 
     * @param sensitiveData 敏感数据
     * @return 脱敏后的数据
     */
    public static String maskSensitiveData(String sensitiveData) {
        if (sensitiveData == null || sensitiveData.length() <= 4) {
            return "****";
        }
        
        // 保留前2位和后2位，中间用*替换
        int length = sensitiveData.length();
        StringBuilder masked = new StringBuilder();
        
        if (length <= 6) {
            // 短字符串只显示首尾各1位
            masked.append(sensitiveData.charAt(0));
            for (int i = 1; i < length - 1; i++) {
                masked.append("*");
            }
            masked.append(sensitiveData.charAt(length - 1));
        } else {
            // 长字符串显示首尾各2位
            masked.append(sensitiveData.substring(0, 2));
            for (int i = 2; i < length - 2; i++) {
                masked.append("*");
            }
            masked.append(sensitiveData.substring(length - 2));
        }
        
        return masked.toString();
    }

    /**
     * 检查是否为生产环境
     * 在生产环境中应该更加严格地控制日志输出
     * 
     * @return 是否为生产环境
     */
    public static boolean isProductionEnvironment() {
        String profile = System.getProperty("spring.profiles.active");
        return "prod".equals(profile) || "production".equals(profile);
    }

    /**
     * 安全地记录包含敏感信息的日志
     * 
     * @param logger 日志记录器
     * @param level 日志级别
     * @param message 消息模板
     * @param sensitiveData 敏感数据
     */
    public static void logWithSensitiveData(Logger logger, String level, String message, String sensitiveData) {
        if (logger == null) {
            return;
        }
        
        String maskedData = maskSensitiveData(sensitiveData);
        String logMessage = message.replace("{}", maskedData);
        
        switch (level.toLowerCase()) {
            case "error":
                logger.error(logMessage);
                break;
            case "warn":
                logger.warn(logMessage);
                break;
            case "info":
                logger.info(logMessage);
                break;
            case "debug":
                // DEBUG级别可以记录更多信息，但仍需脱敏
                if (!isProductionEnvironment()) {
                    logger.debug(logMessage);
                }
                break;
            default:
                logger.info(logMessage);
        }
    }

    /**
     * 获取异常的安全描述信息
     * 
     * @param exception 异常对象
     * @return 安全的异常描述
     */
    public static String getSecureExceptionMessage(Exception exception) {
        if (exception == null) {
            return "Unknown exception";
        }
        
        // 只返回异常类型和简要消息，避免暴露堆栈信息
        String className = exception.getClass().getSimpleName();
        String message = exception.getMessage();
        
        // 如果消息包含敏感信息，进行脱敏处理
        if (message != null && message.length() > 50) {
            message = message.substring(0, 47) + "...";
        }
        
        return className + (message != null ? ": " + message : "");
    }
}
